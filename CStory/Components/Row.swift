//
//  Row.swift
//  CStory
//
//  Created by n<PERSON><PERSON> on 2025/8/20.
//

import SwiftUI

struct Row: View {
  // 可选参数，传值就显示，不传就不显示
  let leftIconName: String?
  let imageName: String?
  let leftMainText: String?
  let leftSubText: String?
  let tagText: String?
  let rightMainText: String?
  let rightSubText: String?
  let rightMainIcon: String?
  let rightSubIcon: String?

  // 初始化方法，所有参数都是可选的
  init(
    leftIconName: String? = nil,
    imageName: String? = nil,
    leftMainText: String? = nil,
    leftSubText: String? = nil,
    tagText: String? = nil,
    rightMainText: String? = nil,
    rightSubText: String? = nil,
    rightMainIcon: String? = nil,
    rightSubIcon: String? = nil
  ) {
    self.leftIconName = leftIconName
    self.imageName = imageName
    self.leftMainText = leftMainText
    self.leftSubText = leftSubText
    self.tagText = tagText
    self.rightMainText = rightMainText
    self.rightSubText = rightSubText
    self.rightMainIcon = rightMainIcon
    self.rightSubIcon = rightSubIcon
  }

  var body: some View {
    HStack(alignment: .center) {
      HStack(spacing: 12.0) {
        if let leftIconName = leftIconName {
          Button(action: {}) {
            Image(leftIconName)
              .font(.system(size: 20, weight: .regular))
              .foregroundColor(.cBlack.opacity(0.4))
              .frame(width: 24, height: 24)
          }
        }
        // 只有传入 imageName 时才显示图片
        if let imageName = imageName {
          Image(imageName)
            .resizable()
            .frame(width: 44, height: 44)
            .cornerRadius(16)
        }
        VStack(alignment: .leading, spacing: 4.0) {
          HStack(alignment: .center) {
            // 只有传入 leftMainText 时才显示左主文案
            if let leftMainText = leftMainText {
              Text(leftMainText)
                .font(.system(size: 14, weight: .medium))
                .foregroundColor(.cBlack)
            }
            // 只有传入 leftSubText 时才显示左副文案
            if let leftSubText = leftSubText {
              Text(leftSubText)
                .font(.system(size: 10, weight: .medium))
                .foregroundColor(.cWhite)
                .padding(.horizontal, 4)
                .padding(.vertical, 2)
                .background(.cAccentRed)
                .cornerRadius(12)
            }
          }
          // 只有传入 tagText 时才显示标签文案
          if let tagText = tagText {
            Text(tagText)
              .font(.system(size: 12, weight: .regular))
              .foregroundColor(.cBlack.opacity(0.4))
          }
        }
      }
      Spacer()
      HStack(alignment: .center, spacing: 0.0) {
        VStack(alignment: .trailing, spacing: 4.0) {
          // 只有传入 rightMainText 时才显示右主文案
          if let rightMainText = rightMainText {
            Text(rightMainText)
              .font(.system(size: 14, weight: .medium))
              .foregroundColor(.cBlack)
          }
          // 只有传入 rightSubText 时才显示右副文案
          if let rightSubText = rightSubText {
            Text(rightSubText)
              .font(.system(size: 12, weight: .regular))
              .foregroundColor(.cBlack.opacity(0.4))
          }
        }
        .padding(.trailing, 8.0)
        HStack(spacing: 8.0) {
          if let rightMainIcon = rightMainIcon {
            Button(action: {}) {
              Image(rightMainIcon)
                .font(.system(size: 20, weight: .regular))
                .foregroundColor(.cBlack.opacity(0.4))
                .frame(width: 24, height: 24)
            }
          }
          if let rightSubIcon = rightSubIcon {
            Button(action: {}) {
              Image(rightSubIcon)
                .font(.system(size: 20, weight: .regular))
                .foregroundColor(.cBlack.opacity(0.4))
                .frame(width: 24, height: 24)
            }
          }
        }

      }

    }
    .padding(.vertical, 8)
    .padding(.horizontal, 12)
    .background(.cWhite.opacity(0.5))
    .cornerRadius(24)
    .padding(.horizontal, 16)
    
  }
}

#Preview {
  VStack(spacing: 12.0) {
    Spacer()
    // 示例：传入所有参数
    Row(
      leftIconName: "circle-placeholder-on",
      imageName: "爱西爱西爱银行",
      leftMainText: "左主文案",
      leftSubText: "左副文案",
      tagText: "标签文案",
      rightMainText: "右主文案",
      rightSubText: "右副文案",
      rightMainIcon: "highlight",
      rightSubIcon: "circle-placeholder-on"
    )
      Row(
        leftIconName: "circle-placeholder-on",
        imageName: "爱西爱西爱银行",
        leftMainText: "左主文案",
        leftSubText: "左副文案",
        rightMainText: "右主文案",
        rightSubText: "右副文案",
        rightMainIcon: "highlight",
        rightSubIcon: "circle-placeholder-on"
      )
      Row(
        imageName: "爱西爱西爱银行",
        leftMainText: "只有主文案",
        rightMainIcon: "highlight",
      )
   
    Row(
      imageName: "爱西爱西爱银行",
      leftMainText: "只有主文案"
    )
     
    Spacer()
  }
  .background(.cBeige)
}
